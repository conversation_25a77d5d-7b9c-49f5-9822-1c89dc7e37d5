import ContactCard from "../components/ContactCard";
import {
    ChatsIcon,
    MapPinIcon,
    PhoneCallIcon,
    RocketIcon,
} from "@phosphor-icons/react";
import ScrollReveal from "../components/ScrollReveal";
import SeoHead from "../components/SeoHead";
import { useForm } from "@inertiajs/react";

const Contact = ({ seoData }) => {
    const { data, setData, post, processing, errors, reset } = useForm({
        name: "",
        company_name: "",
        phone_number: "",
        email: "",
        message: "",
    });

    const handleSubmit = (e) => {
        e.preventDefault();
        post("/contact", {
            onSuccess: () => {
                reset();
            },
        });
    };
    return (
        <>
            <SeoHead
                title={seoData?.meta?.title}
                description={seoData?.meta?.description}
                keywords={seoData?.meta?.keywords}
                canonical={seoData?.meta?.canonical}
                ogTitle={seoData?.meta?.["og:title"]}
                ogDescription={seoData?.meta?.["og:description"]}
                ogImage={seoData?.meta?.["og:image"]}
                ogUrl={seoData?.meta?.["og:url"]}
                ogType={seoData?.meta?.["og:type"]}
                twitterTitle={seoData?.meta?.["twitter:title"]}
                twitterDescription={seoData?.meta?.["twitter:description"]}
                twitterImage={seoData?.meta?.["twitter:image"]}
                structuredData={seoData?.structuredData}
            />
            <div className="flex min-h-screen w-full flex-col items-center bg-white px-2 py-12">
                {/* Header Section */}
                <ScrollReveal className="mb-10 flex flex-col items-center">
                    <span className="mb-4 rounded-full bg-[#F5F6F9] px-4 py-1 text-sm tracking-widest text-gray-500">
                        PANDA CONTACT
                    </span>
                    <h1 className="tablet:text-4xl tablet:font-bold mb-4 text-center text-2xl text-gray-900">
                        Get in touch with us today!
                    </h1>
                    <p className="tablet:text-lg max-w-xl text-center text-base text-gray-500">
                        Contact our sales and support teams for demos,
                        onboarding assistance, or any product inquiries.
                    </p>
                </ScrollReveal>
                {/* Contact Cards Section */}
                <div className="tablet:flex-row mx-auto mb-12 flex w-full max-w-5xl flex-col gap-6 px-6">
                    <ContactCard
                        icon={
                            <ChatsIcon
                                size={32}
                                weight="duotone"
                                className="text-gray-900"
                            />
                        }
                        title="Message us"
                        description="Send us an email and our team will respond promptly to assist you with your inquiry."
                        address="<EMAIL>"
                        protocol="mailto"
                        cardClassName="border-[8px] w-full"
                    />
                    <ContactCard
                        icon={
                            <PhoneCallIcon
                                size={32}
                                weight="duotone"
                                className="text-gray-900"
                            />
                        }
                        title="Call us"
                        description="Let's have a chat – there's nothing quite like talking to another person."
                        address="+92 ************"
                        protocol="tel"
                        cardClassName="border-[8px] w-full"
                    />
                    <ContactCard
                        icon={
                            <MapPinIcon
                                size={32}
                                weight="duotone"
                                className="text-gray-900"
                            />
                        }
                        title="Address"
                        description="We'd be delighted to welcome you to our Head Office."
                        address={"Karachi, Pakistan"}
                        cardClassName="border-[8px] w-full"
                    />
                </div>
                {/* Contact Form Section */}
                <ScrollReveal className="tablet:flex-row flex w-full max-w-6xl flex-col justify-between gap-8 rounded-3xl border-10 border-[#F5F6F9] bg-white p-8 shadow-sm">
                    {/* Left Info */}
                    <div className="tablet:mb-0 mb-6 flex flex-1 flex-col">
                        <div className="mb-3 flex items-center">
                            <RocketIcon
                                size={32}
                                weight="duotone"
                                className="mr-3 text-gray-900"
                            />
                        </div>
                        <div className="mb-3 flex items-center">
                            <span className="text-2xl font-normal text-gray-900">
                                Feel free to send our friendly team a message
                            </span>
                        </div>
                        <div className="mb-3 text-sm text-[#383838]">
                            Message us using our online chat system for quick
                            and efficient support.
                        </div>
                        <div className="mt-2 flex flex-wrap gap-4 text-sm text-[#313131]">
                            <span>Free 7-day trial</span>
                            <span>No credit card required</span>
                            <span>Cancel anytime</span>
                        </div>
                    </div>
                    {/* Right Form */}
                    <form
                        onSubmit={handleSubmit}
                        className="tablet:ml-auto flex w-full max-w-[420px] flex-col gap-4"
                    >
                        <div className="w-full">
                            <input
                                name="name"
                                type="text"
                                placeholder="Name*"
                                value={data.name}
                                onChange={(e) =>
                                    setData("name", e.target.value)
                                }
                                required
                                className={`w-full rounded-lg bg-[#F5F6F9] px-4 py-2 text-sm text-gray-900 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none ${
                                    errors.name
                                        ? "border border-red-300"
                                        : ""
                                }`}
                            />
                            {errors.name && (
                                <p className="mt-1 text-sm text-red-600">
                                    {errors.name}
                                </p>
                            )}
                        </div>
                        <div className="flex gap-4">
                            <div className="tablet:w-full w-1/2">
                                <input
                                    name="company_name"
                                    type="text"
                                    placeholder="Company Name*"
                                    value={data.company_name}
                                    onChange={(e) =>
                                        setData("company_name", e.target.value)
                                    }
                                    required
                                    className={`w-full rounded-lg bg-[#F5F6F9] px-4 py-2 text-sm text-gray-900 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none ${
                                        errors.company_name
                                            ? "border border-red-300"
                                            : ""
                                    }`}
                                />
                                {errors.company_name && (
                                    <p className="mt-1 text-sm text-red-600">
                                        {errors.company_name}
                                    </p>
                                )}
                            </div>
                            <div className="tablet:w-full w-1/2">
                                <input
                                    name="phone_number"
                                    type="text"
                                    placeholder="Phone Number*"
                                    value={data.phone_number}
                                    onChange={(e) =>
                                        setData("phone_number", e.target.value)
                                    }
                                    required
                                    className={`w-full rounded-lg bg-[#F5F6F9] px-4 py-2 text-sm text-gray-900 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none ${
                                        errors.phone_number
                                            ? "border border-red-300"
                                            : ""
                                    }`}
                                />
                                {errors.phone_number && (
                                    <p className="mt-1 text-sm text-red-600">
                                        {errors.phone_number}
                                    </p>
                                )}
                            </div>
                        </div>
                        <div className="w-full">
                            <input
                                name="email"
                                type="email"
                                placeholder="Email Address*"
                                value={data.email}
                                onChange={(e) =>
                                    setData("email", e.target.value)
                                }
                                required
                                className={`w-full rounded-lg bg-[#F5F6F9] px-4 py-2 text-sm text-gray-900 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none ${
                                    errors.email ? "border border-red-300" : ""
                                }`}
                            />
                            {errors.email && (
                                <p className="mt-1 text-sm text-red-600">
                                    {errors.email}
                                </p>
                            )}
                        </div>
                        <div className="w-full">
                            <textarea
                                name="message"
                                placeholder="How we may best assist you?*"
                                value={data.message}
                                onChange={(e) =>
                                    setData("message", e.target.value)
                                }
                                required
                                className={`h-32 w-full rounded-lg bg-[#F5F6F9] px-4 py-2 text-sm text-gray-900 focus:ring-2 focus:ring-[rgb(255,108,10)] focus:outline-none ${
                                    errors.message
                                        ? "border border-red-300"
                                        : ""
                                }`}
                            />
                            {errors.message && (
                                <p className="mt-1 text-sm text-red-600">
                                    {errors.message}
                                </p>
                            )}
                        </div>
                        <button
                            type="submit"
                            disabled={processing}
                            className="mt-2 w-full rounded-lg bg-[#1f1f1f] py-2 text-sm font-medium text-white transition hover:cursor-pointer hover:bg-[rgb(56,56,56)] disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            {processing ? "Submitting..." : "Submit"}
                        </button>
                    </form>
                </ScrollReveal>
            </div>
        </>
    );
};

export default Contact;
